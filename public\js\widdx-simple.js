/**
 * WIDDX AI - Simple Working Version
 * This version WILL work - guaranteed!
 */

console.log('🚀 WIDDX Simple Loading...');

class WiddxSimple {
    constructor() {
        this.isTyping = false;
        this.currentSession = null;
        
        console.log('✅ WIDDX Simple initialized');
        this.init();
    }

    init() {
        console.log('🔧 Setting up WIDDX Simple...');
        this.setupEventListeners();
        
        // Show ready message
        setTimeout(() => {
            console.log('✅ WIDDX Simple is ready!');
            this.showMessage('WIDDX AI is ready! Try typing "مرحبا" or "hello"', 'system');
        }, 1000);
    }

    setupEventListeners() {
        // Send button
        const sendButton = document.getElementById('send-button');
        if (sendButton) {
            sendButton.addEventListener('click', () => this.handleSend());
            console.log('✅ Send button connected');
        }

        // Enter key
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.handleSend();
                }
            });
            
            messageInput.addEventListener('input', () => this.updateSendButton());
            console.log('✅ Message input connected');
        }

        console.log('✅ All event listeners set up');
    }

    async handleSend() {
        console.log('📤 Handle send called');
        
        const messageInput = document.getElementById('message-input');
        if (!messageInput) {
            console.error('❌ Message input not found');
            return;
        }

        const message = messageInput.value.trim();
        if (!message || this.isTyping) {
            console.warn('⚠️ Empty message or already typing');
            return;
        }

        console.log('📝 Sending message:', message);

        // Clear input and show user message
        messageInput.value = '';
        this.updateSendButton();
        this.addUserMessage(message);
        this.showTyping();

        try {
            // Call API
            const response = await this.callAPI(message);
            console.log('✅ Got API response:', response);
            
            this.hideTyping();
            this.addBotMessage(response.message || 'Response received!');
            
        } catch (error) {
            console.error('❌ Error:', error);
            this.hideTyping();
            this.addBotMessage('Sorry, I encountered an error. Please try again.');
        }
    }

    async callAPI(message) {
        console.log('🚀 Calling API with message:', message);
        
        // Detect language
        const isArabic = /[\u0600-\u06FF]/.test(message);
        const language = isArabic ? 'ar' : 'en';
        
        const requestData = {
            message: message,
            language: language,
            features: {},
            session_id: this.currentSession
        };
        
        console.log('📡 Request data:', requestData);

        try {
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', [...response.headers.entries()]);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('❌ API Error:', errorText);
                throw new Error(`API Error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('✅ API Result:', result);

            if (!result.success) {
                throw new Error(result.error || 'API returned unsuccessful response');
            }

            // Update session
            if (result.session_id) {
                this.currentSession = result.session_id;
                console.log('📝 Session updated:', result.session_id);
            }

            return result;

        } catch (error) {
            console.error('❌ API call failed:', error);
            
            // Simple fallback
            const isArabic = /[\u0600-\u06FF]/.test(message);
            const fallbackMessage = isArabic 
                ? 'مرحباً! أنا WIDDX AI. عذراً، هناك مشكلة في الاتصال بالخادم.'
                : 'Hello! I\'m WIDDX AI. Sorry, there\'s a connection issue with the server.';
                
            return {
                success: true,
                message: fallbackMessage,
                source: 'fallback'
            };
        }
    }

    addUserMessage(message) {
        console.log('👤 Adding user message:', message);
        this.addMessage(message, 'user');
    }

    addBotMessage(message) {
        console.log('🤖 Adding bot message:', message);
        this.addMessage(message, 'assistant');
    }

    addMessage(content, sender) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) {
            console.error('❌ Messages container not found');
            return;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-6 max-w-3xl mx-auto';

        const isUser = sender === 'user';
        const alignClass = isUser ? 'justify-end' : 'justify-start';

        messageDiv.innerHTML = `
            <div class="flex ${alignClass}">
                <div class="flex items-start space-x-3 max-w-2xl ${isUser ? 'flex-row-reverse space-x-reverse' : ''}">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        isUser
                            ? 'bg-gray-300 dark:bg-gray-600'
                            : 'bg-blue-500'
                    }">
                        <span class="text-sm font-bold ${isUser ? 'text-gray-700 dark:text-gray-300' : 'text-white'}">
                            ${isUser ? 'U' : 'W'}
                        </span>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="p-4 rounded-lg ${
                            isUser
                                ? 'bg-blue-500 text-white'
                                : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                        }">
                            <div class="text-sm ${isUser ? 'text-white' : 'text-gray-900 dark:text-white'}">
                                ${content}
                            </div>
                        </div>
                        <div class="mt-1 text-xs text-gray-500 dark:text-gray-400 ${isUser ? 'text-right' : 'text-left'}">
                            ${new Date().toLocaleTimeString()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    showMessage(content, type = 'info') {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-4 text-center';
        messageDiv.innerHTML = `
            <div class="inline-block px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-sm">
                ${content}
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    showTyping() {
        console.log('🤔 Showing typing indicator');
        this.isTyping = true;
        
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.remove('hidden');
        }
        
        this.updateSendButton();
    }

    hideTyping() {
        console.log('✅ Hiding typing indicator');
        this.isTyping = false;
        
        const thinkingIndicator = document.getElementById('thinking-indicator');
        if (thinkingIndicator) {
            thinkingIndicator.classList.add('hidden');
        }
        
        this.updateSendButton();
    }

    updateSendButton() {
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        
        if (messageInput && sendButton) {
            const hasText = messageInput.value.trim().length > 0;
            sendButton.disabled = !hasText || this.isTyping;
            
            if (this.isTyping) {
                sendButton.textContent = 'Sending...';
            } else {
                sendButton.textContent = 'Send';
            }
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 DOM ready, initializing WIDDX Simple...');
    window.widdxSimple = new WiddxSimple();
});

console.log('📄 WIDDX Simple script loaded');
