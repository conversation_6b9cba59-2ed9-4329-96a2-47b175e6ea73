<?php

use App\Http\Controllers\ChatController;
use App\Http\Controllers\SimpleChatController;
use App\Http\Controllers\IntelligentChatController;
use App\Http\Controllers\AdvancedFeaturesController;
use App\Http\Controllers\UnlimitedSearchController;
use App\Http\Controllers\DeepSeekSearchController;
use App\Http\Controllers\ImageGenerationController;
use App\Http\Controllers\UserPreferencesController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// WIDDX AI Chat Routes

// Intelligent Chat Routes (AI-powered with DeepSeek & Gemini)
Route::prefix('chat')->group(function () {
    Route::post('/', [IntelligentChatController::class, 'chat']);
    Route::get('/health', [IntelligentChatController::class, 'health']);
    Route::get('/features', [IntelligentChatController::class, 'features']);
});

// Simple Chat Routes (fallback and testing)
Route::prefix('chat-simple')->group(function () {
    Route::post('/', [SimpleChatController::class, 'chat']);
    Route::get('/health', [SimpleChatController::class, 'health']);
    Route::get('/features', [SimpleChatController::class, 'features']);
});

// Advanced Chat Routes (original complex system)
Route::prefix('chat-advanced')->middleware('widdx.rate_limit')->group(function () {
    Route::post('/', [ChatController::class, 'chat']);
    Route::get('/personalities', [ChatController::class, 'getPersonalities']);
    Route::get('/history', [ChatController::class, 'getSessionHistory']);
    Route::put('/personality', [ChatController::class, 'updatePersonality']);
});

// Advanced Features Routes
Route::prefix('features')->middleware('widdx.rate_limit')->group(function () {
    // Test image generation endpoint
    Route::get('/test-image', function (\App\Services\ImageGenerationService $imageService) {
        try {
            // Get available providers from the service
            $availableOptions = $imageService->getAvailableOptions();
            $availableProviders = $availableOptions['providers'] ?? [];

            if (empty($availableProviders)) {
                throw new \Exception('No image generation providers are available');
            }

            // Try to use a real provider first, then fallback to free alternatives
            $preferredProviders = ['gemini', 'huggingface', 'deepseek'];
            $provider = null;

            foreach ($preferredProviders as $pref) {
                if (in_array($pref, $availableProviders)) {
                    $provider = $pref;
                    break;
                }
            }

            // If no real provider available, use free alternative
            if (!$provider) {
                $provider = $availableProviders[0];
            }

            $prompt = 'A beautiful sunset over mountains';
            $options = [
                'provider' => $provider,
                'style' => 'natural',
                'quality' => 'standard',
            ];

            \Illuminate\Support\Facades\Log::info('Testing image generation', [
                'provider' => $provider,
                'prompt' => $prompt
            ]);

            $result = $imageService->generateImage($prompt, $options);

            return response()->json([
                'success' => true,
                'provider' => $provider,
                'available_providers' => $availableProviders,
                'result' => $result,
                'message' => 'اختبار توليد الصور تم بنجاح'
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Image generation test failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'available_providers' => $availableProviders ?? [],
                'message' => 'فشل في اختبار توليد الصور'
            ], 500);
        }
    });

    // Live Search (Free & Paid)
    Route::post('/search', [AdvancedFeaturesController::class, 'search']);
    Route::post('/search-suggestions', [AdvancedFeaturesController::class, 'searchSuggestions']);
    Route::post('/deep-search', [AdvancedFeaturesController::class, 'deepSearch']);

    // Image Generation Routes
    Route::post('/generate-image', [ImageGenerationController::class, 'generate']);

    Route::post('/generate-placeholder', [AdvancedFeaturesController::class, 'generatePlaceholder']);

    // Voice Services (Free & Paid)
    Route::post('/text-to-speech', [AdvancedFeaturesController::class, 'textToSpeech']);
    Route::post('/speech-to-text', [AdvancedFeaturesController::class, 'speechToText']);
    Route::get('/speech-to-text-instructions', [AdvancedFeaturesController::class, 'speechToTextInstructions']);
    Route::get('/voice-widget', [AdvancedFeaturesController::class, 'voiceWidget']);

    // Think Mode
    Route::post('/think-mode', [AdvancedFeaturesController::class, 'thinkMode']);

    // Document Analysis
    Route::post('/analyze-document', [AdvancedFeaturesController::class, 'analyzeDocument']);

    // Vision/Image Analysis
    Route::post('/analyze-image', [AdvancedFeaturesController::class, 'analyzeImage']);

    // Capabilities
    Route::get('/capabilities', [AdvancedFeaturesController::class, 'getCapabilities']);
});

// Unlimited Search Routes (No Rate Limiting)
Route::prefix('unlimited-search')->middleware('unlimited.search')->group(function () {
    Route::post('/', [UnlimitedSearchController::class, 'search']);
    Route::post('/bulk', [UnlimitedSearchController::class, 'bulkSearch']);
    Route::post('/suggestions', [UnlimitedSearchController::class, 'suggestions']);
    Route::get('/capabilities', [UnlimitedSearchController::class, 'capabilities']);
});

// DeepSeek Intelligent Search Routes (AI-Powered)
Route::prefix('deepseek-search')->middleware('unlimited.search')->group(function () {
    Route::post('/intelligent', [DeepSeekSearchController::class, 'intelligentSearch']);
    Route::post('/comparative', [DeepSeekSearchController::class, 'comparativeSearch']);
    Route::post('/interactive', [DeepSeekSearchController::class, 'interactiveSearch']);
    Route::get('/capabilities', [DeepSeekSearchController::class, 'capabilities']);
});

// User preferences and settings endpoints
Route::prefix('preferences')->middleware('widdx.rate_limit')->group(function () {
    // Get all preferences
    Route::get('/', [UserPreferencesController::class, 'getAllPreferences']);

    // Category-specific preferences
    Route::get('/category/{category}', [UserPreferencesController::class, 'getCategoryPreferences']);
    Route::put('/category/{category}', [UserPreferencesController::class, 'setCategoryPreferences']);

    // Specific preference
    Route::get('/category/{category}/{key}', [UserPreferencesController::class, 'getPreference']);
    Route::put('/category/{category}/{key}', [UserPreferencesController::class, 'setPreference']);

    // Import/Export
    Route::get('/export', [UserPreferencesController::class, 'exportPreferences']);
    Route::post('/import', [UserPreferencesController::class, 'importPreferences']);
    Route::post('/reset', [UserPreferencesController::class, 'resetToDefaults']);

    // Schema
    Route::get('/schema', [UserPreferencesController::class, 'getPreferenceSchema']);
});

// Feature toggle endpoints
Route::prefix('feature-toggles')->middleware('widdx.rate_limit')->group(function () {
    // Available features
    Route::get('/available', [UserPreferencesController::class, 'getAvailableFeatures']);

    // User features
    Route::get('/user', [UserPreferencesController::class, 'getUserFeatures']);

    // Enable/Disable features
    Route::post('/{featureKey}/enable', [UserPreferencesController::class, 'enableFeature']);
    Route::post('/{featureKey}/disable', [UserPreferencesController::class, 'disableFeature']);
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'service' => 'WIDDX AI',
        'timestamp' => now()->toISOString(),
        'features' => [
            'live_search' => true,
            'image_generation' => true,
            'voice_services' => true,
            'deep_search' => true,
            'think_mode' => true,
            'document_analysis' => true,
            'vision' => true,
            'deep_thinking' => true,
            'ultra_deep_search' => true,
            'user_preferences' => true,
            'feature_toggles' => true,
        ],
    ]);
});
