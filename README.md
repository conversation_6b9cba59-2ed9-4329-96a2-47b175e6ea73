# WIDDX AI - Intelligent Assistant Platform

[![<PERSON><PERSON>](https://img.shields.io/badge/Laravel-12.0-red.svg)](https://laravel.com)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-4.0-blue.svg)](https://tailwindcss.com)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-orange.svg)](https://mysql.com)
[![PHP](https://img.shields.io/badge/PHP-8.2+-purple.svg)](https://php.net)

WIDDX AI is an advanced intelligent assistant platform featuring multi-model AI integration, real-time chat capabilities, image generation, multilingual support, and a sophisticated learning system. Built with Laravel and modern web technologies, WIDDX provides a professional, mobile-first interface similar to GROK's design philosophy.

## 📋 Table of Contents

- [Features](#-features)
- [Architecture Overview](#-architecture-overview)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Usage](#-usage)
- [API Integration](#-api-integration)
- [Development](#-development)
- [Testing](#-testing)
- [Troubleshooting](#-troubleshooting)
- [Contributing](#-contributing)

## ✨ Features

### 🤖 Multi-Model AI Integration
- **DeepSeek API**: Primary model for search and analytical tasks
- **Gemini API**: Secondary model for general queries and image generation
- **Intelligent Model Switching**: Automatic fallback and load balancing
- **Model Comparison**: Optional side-by-side response comparison

### 💬 Advanced Chat System
- **Real-time Conversations**: Instant messaging with typing indicators
- **Session Management**: Persistent conversation history and context
- **Message Threading**: Organized conversation flow
- **Smart Context**: AI learns from conversation patterns

### 🌍 Multilingual Support
- **Automatic Language Detection**: Uses `landrok/language-detector`
- **Dynamic Response Language**: Responds in user's detected language
- **Arabic & English**: Full RTL support and language-specific UI
- **Database Language Tagging**: Conversations stored with language metadata

### 🎨 Text-to-Image Generation
- **Gemini 2.0 Flash**: High-quality image generation
- **Free Alternatives**: Multiple fallback providers for cost-effective generation
- **Smart Prompt Analysis**: AI-enhanced prompt optimization
- **Image Storage**: Local storage with cleanup management

### 🔍 Intelligent Search
- **DeepSeek-Powered Search**: AI-enhanced query processing
- **Unlimited Requests**: No rate limiting on search functionality
- **Multi-Source Results**: DuckDuckGo and other search providers
- **Relevance Scoring**: AI-driven result ranking and analysis

### 🧠 Deep Thinking Mode
- **Step-by-Step Analysis**: Complex problem breakdown
- **Multi-Stage Reasoning**: Layered analytical approach
- **Contextual Insights**: Deep understanding of user queries

### 🎯 WIDDX Identity System
- **Private AI Identity**: Independent learning and knowledge building
- **Personality Evolution**: Dynamic personality development
- **Knowledge Base**: Self-building information repository
- **Learning Sessions**: Continuous improvement from interactions

### 🎨 Professional Interface
- **Mobile-First Design**: Responsive, touch-friendly interface
- **Dark Theme**: Professional dark UI optimized for extended use
- **GROK-Inspired Design**: Clean, modern interface similar to GROK
- **Feature Toggles**: Optional deep thinking mode toggle
- **Keyboard Shortcuts**: Power user productivity features

## 🏗️ Architecture Overview

### Backend Structure
```
app/
├── Console/Commands/     # Artisan commands for testing and maintenance
├── Http/Controllers/     # API and web controllers
├── Models/              # Database models and relationships
├── Services/            # Core business logic and AI integrations
│   ├── DeepSeekClient.php
│   ├── GeminiClient.php
│   ├── ImageGenerationService.php
│   ├── LanguageDetectionService.php
│   ├── WiddxIdentityService.php
│   ├── WiddxKnowledgeService.php
│   └── WiddxLearningService.php
└── ValueObjects/        # Data transfer objects
```

### Database Schema
- **Chat Sessions**: Conversation management and persistence
- **Messages**: Individual message storage with metadata
- **Knowledge Entries**: WIDDX's learned information
- **Personality Evolution**: Dynamic personality development tracking
- **User Patterns**: Behavioral analysis and preferences
- **Context Memory**: Long-term conversation context

### Frontend Architecture
- **Modern JavaScript**: ES6+ with modular design
- **TailwindCSS 4.0**: Utility-first CSS framework
- **Vite**: Fast build tool and development server
- **Progressive Enhancement**: Works across all devices and browsers

## 🚀 Installation

### Prerequisites
- **PHP 8.2+** with required extensions (ctype, filter, hash, mbstring, openssl, session, tokenizer)
- **MySQL 8.0+** (configured with root user, empty password, port 3306)
- **Node.js 18+** and npm
- **Composer 2.0+**

### Step-by-Step Setup

1. **Clone the Repository**
   ```bash
   git clone <repository-url> widdx-ai
   cd widdx-ai
   ```

2. **Install PHP Dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js Dependencies**
   ```bash
   npm install
   ```

4. **Environment Configuration**
   ```bash
   # Copy the MySQL environment template
   cp .env.mysql .env

   # Generate application key
   php artisan key:generate
   ```

5. **Database Setup**
   ```bash
   # Create the database
   mysql -u root -p -e "CREATE DATABASE widdx_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

   # Run migrations
   php artisan migrate

   # Seed initial data (optional)
   php artisan db:seed
   ```

6. **Build Frontend Assets**
   ```bash
   # For development
   npm run dev

   # For production
   npm run build
   ```

7. **Start the Application**
   ```bash
   # Development server
   php artisan serve

   # Or use the comprehensive development command
   composer run dev
   ```

8. **Access WIDDX AI**
   - Main Interface: `http://localhost:8000`
   - Settings: `http://localhost:8000/settings`
   - Help: `http://localhost:8000/help`

## ⚙️ Configuration

### Environment Variables

Create your `.env` file based on `.env.mysql` and configure the following:

#### Database Configuration
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=widdx_ai
DB_USERNAME=root
DB_PASSWORD=
```

#### API Keys (Required)
```env
# DeepSeek API (Primary model for search and analysis)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com
DEEPSEEK_TIMEOUT=30

# Gemini API (Secondary model and image generation)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_BASE_URL=https://generativelanguage.googleapis.com
GEMINI_TIMEOUT=30
```

#### WIDDX AI Settings
```env
# Core Settings
WIDDX_DEFAULT_PERSONALITY=neutral
WIDDX_MAX_MESSAGE_LENGTH=10000
WIDDX_SESSION_CLEANUP_DAYS=30
WIDDX_ENABLE_LOGGING=true

# Model Configuration
WIDDX_PRIMARY_MODEL=deepseek
WIDDX_FALLBACK_MODEL=gemini
WIDDX_ENABLE_MODEL_COMPARISON=false

# Feature Toggles
WIDDX_SEARCH_ENABLED=true
WIDDX_IMAGE_GEN_ENABLED=true
WIDDX_DEEPSEEK_SEARCH_ENABLED=true
WIDDX_USE_FREE_ALTERNATIVES=true
```

### API Key Setup

#### DeepSeek API
1. Visit [DeepSeek Platform](https://platform.deepseek.com)
2. Create an account and generate an API key
3. Add the key to your `.env` file

#### Gemini API
1. Visit [Google AI Studio](https://aistudio.google.com)
2. Create a new API key
3. Add the key to your `.env` file

### Testing Configuration
```bash
# Test API connections
php artisan widdx:test-features

# Test specific features
php artisan widdx:test-features --feature=image
php artisan widdx:test-features --feature=search
php artisan widdx:test-features --feature=multilingual
```

## 📱 Usage

### Basic Chat Interaction
1. **Start a Conversation**: Type your message in the chat input
2. **Send Message**: Press Enter or click the Send button
3. **View Response**: WIDDX responds intelligently based on your input
4. **Continue Conversation**: Context is maintained throughout the session

### Image Generation
WIDDX automatically detects image generation requests:

```
User: "Create an image of a sunset over mountains"
WIDDX: [Generates and displays the image with description]
```

**Manual Image Generation:**
- Click the image generation button in the chat interface
- Enter your prompt in the dedicated modal
- Choose size and quality options
- Generate and download images

### Multilingual Support
- **Automatic Detection**: WIDDX detects your language automatically
- **Response Language**: Responds in the same language as your input
- **Supported Languages**: Arabic, English (with full RTL support)

```
User: "مرحبا، كيف حالك؟"
WIDDX: "مرحبا! أنا بخير، شكراً لك. كيف يمكنني مساعدتك اليوم؟"
```

### Deep Thinking Mode
Toggle deep thinking for complex analysis:
- Click the thinking mode toggle
- Ask complex questions requiring step-by-step analysis
- View detailed reasoning process

### Search Functionality
WIDDX provides unlimited internet search:
```
User: "What's the latest news about AI developments?"
WIDDX: [Searches and provides current information with sources]
```

## 🔌 API Integration

### REST API Endpoints

#### Chat API
```bash
POST /api/chat
Content-Type: application/json

{
  "message": "Hello, how are you?",
  "session_id": "optional-session-id",
  "language": "auto",
  "features": {
    "thinking_mode": false,
    "search_enabled": true,
    "image_generation": true
  }
}
```

#### Image Generation API
```bash
POST /api/generate-image
Content-Type: application/json

{
  "prompt": "A beautiful sunset over mountains",
  "size": "1024x1024",
  "quality": "standard",
  "style": "natural"
}
```

#### Search API
```bash
POST /api/search
Content-Type: application/json

{
  "query": "latest AI developments",
  "language": "en",
  "max_results": 10
}
```

### JavaScript Integration
```javascript
// Initialize WIDDX Chat
const widdx = new WiddxChat({
  apiUrl: '/api/chat',
  features: {
    imageGeneration: true,
    search: true,
    thinkingMode: true
  }
});

// Send message
widdx.sendMessage('Hello WIDDX!');

// Generate image
widdx.generateImage('A beautiful landscape');
```

## 🛠️ Development

### Development Environment
```bash
# Start all development services
composer run dev

# Individual services
php artisan serve          # Laravel server
npm run dev                # Vite development server
php artisan queue:listen   # Queue worker
php artisan pail           # Log monitoring
```

### Code Structure

#### Adding New Services
1. Create service class in `app/Services/`
2. Implement required interfaces
3. Register in service provider
4. Add configuration to `config/widdx.php`

#### Adding New Features
1. Create controller in `app/Http/Controllers/`
2. Add routes in `routes/api.php` or `routes/web.php`
3. Create frontend components in `public/js/`
4. Update configuration and documentation

### Database Management
```bash
# Create new migration
php artisan make:migration create_new_table

# Run migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

### Asset Management
```bash
# Watch for changes (development)
npm run dev

# Build for production
npm run build

# Analyze bundle size
npm run build -- --analyze
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage

# Test specific feature
php artisan test --filter=ChatTest
```

### Feature Testing
```bash
# Test all WIDDX features
php artisan widdx:test-features

# Test specific features
php artisan widdx:test-features --feature=image
php artisan widdx:test-features --feature=search
php artisan widdx:test-features --feature=multilingual
php artisan widdx:test-features --feature=deepthinking
```

### API Testing
```bash
# Test API endpoints
curl -X POST http://localhost:8000/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello WIDDX!"}'

# Test image generation
curl -X POST http://localhost:8000/api/generate-image \
  -H "Content-Type: application/json" \
  -d '{"prompt": "A beautiful sunset"}'
```

## 🔧 Troubleshooting

### Common Issues

#### API Connection Problems
```bash
# Check API configuration
php artisan widdx:test-features

# Verify environment variables
php artisan config:show widdx

# Clear configuration cache
php artisan config:clear
```

#### Database Issues
```bash
# Check database connection
php artisan migrate:status

# Reset database
php artisan migrate:fresh --seed

# Check MySQL connection
mysql -u root -p -e "SHOW DATABASES;"
```

#### Frontend Build Issues
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Vite cache
rm -rf node_modules/.vite

# Rebuild assets
npm run build
```

#### Image Generation Issues
```bash
# Check image generation service
php artisan widdx:test-features --feature=image

# Check storage permissions
php artisan storage:link

# Clean up old images
php artisan widdx:cleanup-images
```

### Performance Optimization

#### Database Optimization
```bash
# Optimize database
php artisan optimize

# Clear all caches
php artisan optimize:clear

# Queue optimization
php artisan queue:restart
```

#### Frontend Optimization
```bash
# Optimize assets for production
npm run build

# Enable compression in web server
# Add to .htaccess or nginx config
```

### Logging and Debugging
```bash
# Monitor logs in real-time
php artisan pail

# Check specific log files
tail -f storage/logs/laravel.log

# Debug mode (development only)
# Set APP_DEBUG=true in .env
```

## 📚 Documentation

### Available Documentation
- **[Developer Guide](docs/DEVELOPER_GUIDE.md)**: Detailed development instructions
- **[Image Generation Guide](docs/IMAGE_GENERATION.md)**: Image generation features and API
- **[Enhanced UI Guide](docs/ENHANCED_UI_GUIDE.md)**: Frontend interface documentation
- **[Examples](docs/EXAMPLES.md)**: Code examples and use cases

### API Documentation
- **Chat API**: Real-time conversation endpoints
- **Image Generation API**: Text-to-image generation
- **Search API**: Intelligent search functionality
- **User Preferences API**: Personalization settings

## 🤝 Contributing

### Development Workflow
1. **Fork the Repository**
2. **Create Feature Branch**
   ```bash
   git checkout -b feature/new-feature
   ```
3. **Make Changes**
   - Follow PSR-12 coding standards
   - Add tests for new features
   - Update documentation
4. **Run Tests**
   ```bash
   php artisan test
   composer run test
   ```
5. **Submit Pull Request**

### Code Standards
- **PHP**: PSR-12 coding standard
- **JavaScript**: ES6+ with consistent formatting
- **CSS**: TailwindCSS utility classes
- **Documentation**: Markdown with clear examples

### Security
- **Input Validation**: All user inputs are validated and sanitized
- **API Rate Limiting**: Implemented for all endpoints
- **Environment Security**: Sensitive data in environment variables
- **Session Management**: Secure session handling

## 📄 License

This project is proprietary software. All rights reserved.

## 🙏 Acknowledgments

- **Laravel Framework**: Robust PHP framework
- **TailwindCSS**: Utility-first CSS framework
- **DeepSeek**: Advanced AI model for search and analysis
- **Google Gemini**: AI model for general queries and image generation
- **Language Detection**: Landrok language detector library

---

**WIDDX AI** - Where Intelligence Meets Innovation 🚀

For support, please visit our [Help Center](http://localhost:8000/help) or contact the development team.
