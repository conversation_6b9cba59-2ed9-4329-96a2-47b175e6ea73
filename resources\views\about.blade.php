<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX AI - About</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'widdx': {
                            '50': '#eff6ff',
                            '500': '#3B82F6',
                            '600': '#2563EB',
                            '900': '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white min-h-screen">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-widdx-500 to-widdx-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">W</span>
                        </div>
                        <span class="text-xl font-bold">WIDDX AI</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Chat
                    </a>
                    <button id="theme-toggle" class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="hidden fas fa-sun dark:inline"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-widdx-500 to-widdx-600 text-white py-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span class="text-3xl font-bold">W</span>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold mb-4">WIDDX AI</h1>
            <p class="text-xl md:text-2xl text-blue-100 mb-6">Your Advanced Intelligent Assistant</p>
            <p class="text-lg text-blue-200 max-w-2xl mx-auto">Empowering conversations with cutting-edge artificial intelligence, multilingual support, and intelligent feature automation.</p>
        </div>
    </section>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- What is WIDDX AI -->
        <section class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">What is WIDDX AI?</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">WIDDX AI is a sophisticated artificial intelligence assistant designed to understand, learn, and assist with a wide range of tasks through natural conversation.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-widdx-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-brain text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Intelligent</h3>
                    <p class="text-gray-600 dark:text-gray-400">Advanced AI that understands context, learns from conversations, and provides thoughtful responses.</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-widdx-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-globe text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Multilingual</h3>
                    <p class="text-gray-600 dark:text-gray-400">Supports multiple languages with automatic detection and natural responses in your preferred language.</p>
                </div>

                <div class="text-center">
                    <div class="w-16 h-16 bg-widdx-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-magic text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">Adaptive</h3>
                    <p class="text-gray-600 dark:text-gray-400">Automatically activates features based on your needs, making interactions seamless and intuitive.</p>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Powerful Features</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400">WIDDX AI comes packed with advanced capabilities to assist you in various tasks.</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-image text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Image Generation</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Create stunning images from text descriptions with advanced AI-powered image generation.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-language text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Translation</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Translate text between multiple languages with high accuracy and natural phrasing.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-code text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Programming</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Get help with coding, debugging, and learning programming languages and frameworks.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-search text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Web Search</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Access current information from the web with intelligent search capabilities.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-brain text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Think Mode</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Deep reasoning and step-by-step analysis for complex problems and questions.</p>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-eye text-widdx-500 text-xl mr-3"></i>
                        <h3 class="text-lg font-semibold">Vision Analysis</h3>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Analyze and understand images, providing detailed descriptions and insights.</p>
                </div>
            </div>
        </section>

        <!-- Technology -->
        <section class="mb-16">
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold mb-4">Advanced Technology</h2>
                    <p class="text-lg text-gray-600 dark:text-gray-400">Built with cutting-edge AI technology for superior performance and reliability.</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-semibold mb-4">Intelligent Architecture</h3>
                        <ul class="space-y-2 text-gray-600 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Advanced natural language processing</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Context-aware conversation memory</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Automatic feature detection</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Multi-modal capabilities</li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold mb-4">User Experience</h3>
                        <ul class="space-y-2 text-gray-600 dark:text-gray-400">
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Intuitive interface design</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Dark and light theme support</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Responsive mobile-first design</li>
                            <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Customizable settings</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Version Info -->
        <section class="mb-16">
            <div class="bg-gradient-to-r from-widdx-50 to-blue-50 dark:from-widdx-900/20 dark:to-blue-900/20 rounded-lg border border-widdx-200 dark:border-widdx-800 p-8">
                <div class="text-center">
                    <h2 class="text-2xl font-bold mb-4">Current Version</h2>
                    <div class="text-4xl font-bold text-widdx-500 mb-2">v2.0.0</div>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">Latest stable release with enhanced AI capabilities</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4">
                            <div class="font-semibold text-widdx-500">Release Date</div>
                            <div class="text-gray-600 dark:text-gray-400">January 2024</div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4">
                            <div class="font-semibold text-widdx-500">Status</div>
                            <div class="text-green-600 dark:text-green-400">Stable</div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-4">
                            <div class="font-semibold text-widdx-500">License</div>
                            <div class="text-gray-600 dark:text-gray-400">Proprietary</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact -->
        <section class="text-center">
            <h2 class="text-3xl font-bold mb-4">Get in Touch</h2>
            <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">Have questions or feedback? We'd love to hear from you.</p>
            
            <div class="flex justify-center space-x-6">
                <a href="mailto:<EMAIL>" class="flex items-center px-6 py-3 bg-widdx-500 text-white rounded-lg hover:bg-widdx-600 transition-colors">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Support
                </a>
                <a href="/help" class="flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-question-circle mr-2"></i>
                    Help Center
                </a>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-8">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center text-gray-600 dark:text-gray-400">
                <p>&copy; 2024 WIDDX AI. All rights reserved.</p>
                <p class="mt-2">Built with ❤️ for intelligent conversations</p>
            </div>
        </div>
    </footer>

    <script>
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // Load theme
        window.addEventListener('load', () => {
            const theme = localStorage.getItem('theme');
            if (theme === 'light') {
                document.documentElement.classList.remove('dark');
            } else {
                document.documentElement.classList.add('dark');
            }
        });
    </script>
</body>
</html>
