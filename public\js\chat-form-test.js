/**
 * WIDDX AI - Chat Form Testing
 * اختبار شامل لنموذج الدردشة
 */

class ChatFormTester {
    constructor() {
        this.testResults = [];
        this.totalTests = 0;
        this.passedTests = 0;
        this.failedTests = 0;
        
        console.log('💬 بدء اختبار نموذج الدردشة...');
        this.runChatFormTests();
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${emoji} [${timestamp}] ${message}`);
        
        this.testResults.push({
            timestamp,
            message,
            type
        });
    }

    test(description, testFunction) {
        this.totalTests++;
        try {
            const result = testFunction();
            if (result) {
                this.passedTests++;
                this.log(`PASS: ${description}`, 'success');
                return true;
            } else {
                this.failedTests++;
                this.log(`FAIL: ${description}`, 'error');
                return false;
            }
        } catch (error) {
            this.failedTests++;
            this.log(`ERROR: ${description} - ${error.message}`, 'error');
            return false;
        }
    }

    runChatFormTests() {
        this.log('بدء اختبارات نموذج الدردشة...');

        // Test Form Elements
        this.testFormElements();
        
        // Test Input Functionality
        this.testInputFunctionality();
        
        // Test Send Button
        this.testSendButton();
        
        // Test Character Counter
        this.testCharacterCounter();
        
        // Test Auto-resize
        this.testAutoResize();
        
        // Test Clear Button
        this.testClearButton();
        
        // Test Keyboard Shortcuts
        this.testKeyboardShortcuts();
        
        // Test Form Validation
        this.testFormValidation();
        
        // Test Message Display
        this.testMessageDisplay();

        // Generate Report
        this.generateReport();
    }

    testFormElements() {
        this.log('اختبار عناصر النموذج...');

        this.test('وجود نموذج الدردشة', () => {
            const form = document.getElementById('chat-form');
            return form && form.tagName === 'FORM';
        });

        this.test('وجود حقل إدخال الرسالة', () => {
            const input = document.getElementById('message-input');
            return input && (input.tagName === 'TEXTAREA' || input.tagName === 'INPUT');
        });

        this.test('وجود زر الإرسال', () => {
            const button = document.getElementById('send-button');
            return button && button.tagName === 'BUTTON';
        });

        this.test('وجود عداد الأحرف', () => {
            const counter = document.getElementById('char-count');
            return counter !== null;
        });

        this.test('وجود زر المسح', () => {
            const clearBtn = document.getElementById('clear-input');
            return clearBtn !== null;
        });
    }

    testInputFunctionality() {
        this.log('اختبار وظائف الإدخال...');

        this.test('إدخال النص في الحقل', () => {
            const input = document.getElementById('message-input');
            if (!input) return false;
            
            const testText = 'اختبار النص العربي والإنجليزي Test';
            input.value = testText;
            return input.value === testText;
        });

        this.test('تفعيل زر الإرسال عند وجود نص', () => {
            const input = document.getElementById('message-input');
            const sendBtn = document.getElementById('send-button');
            if (!input || !sendBtn) return false;
            
            input.value = 'نص اختبار';
            input.dispatchEvent(new Event('input'));
            
            // Check if button is enabled (not disabled)
            return !sendBtn.disabled;
        });

        this.test('تعطيل زر الإرسال عند عدم وجود نص', () => {
            const input = document.getElementById('message-input');
            const sendBtn = document.getElementById('send-button');
            if (!input || !sendBtn) return false;
            
            input.value = '';
            input.dispatchEvent(new Event('input'));
            
            return sendBtn.disabled;
        });
    }

    testSendButton() {
        this.log('اختبار زر الإرسال...');

        this.test('النقر على زر الإرسال', () => {
            const sendBtn = document.getElementById('send-button');
            if (!sendBtn) return false;
            
            let clicked = false;
            const originalClick = sendBtn.onclick;
            
            sendBtn.onclick = () => { clicked = true; };
            sendBtn.click();
            sendBtn.onclick = originalClick;
            
            return clicked;
        });

        this.test('إرسال النموذج', () => {
            const form = document.getElementById('chat-form');
            if (!form) return false;
            
            let submitted = false;
            const handler = (e) => {
                e.preventDefault();
                submitted = true;
            };
            
            form.addEventListener('submit', handler);
            
            // Simulate form submission
            const event = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(event);
            
            form.removeEventListener('submit', handler);
            return submitted;
        });
    }

    testCharacterCounter() {
        this.log('اختبار عداد الأحرف...');

        this.test('تحديث عداد الأحرف', () => {
            const input = document.getElementById('message-input');
            const counter = document.getElementById('char-count');
            if (!input || !counter) return false;
            
            const testText = 'اختبار العداد';
            input.value = testText;
            input.dispatchEvent(new Event('input'));
            
            return counter.textContent == testText.length.toString();
        });

        this.test('عداد الأحرف يبدأ من صفر', () => {
            const input = document.getElementById('message-input');
            const counter = document.getElementById('char-count');
            if (!input || !counter) return false;
            
            input.value = '';
            input.dispatchEvent(new Event('input'));
            
            return counter.textContent === '0';
        });
    }

    testAutoResize() {
        this.log('اختبار تغيير الحجم التلقائي...');

        this.test('تغيير حجم النص تلقائياً', () => {
            const input = document.getElementById('message-input');
            if (!input || input.tagName !== 'TEXTAREA') return false;
            
            const initialHeight = input.style.height || input.offsetHeight;
            
            // Add multiple lines
            input.value = 'سطر أول\nسطر ثاني\nسطر ثالث\nسطر رابع';
            input.dispatchEvent(new Event('input'));
            
            const newHeight = input.style.height || input.offsetHeight;
            return newHeight !== initialHeight;
        });
    }

    testClearButton() {
        this.log('اختبار زر المسح...');

        this.test('مسح النص بزر المسح', () => {
            const input = document.getElementById('message-input');
            const clearBtn = document.getElementById('clear-input');
            if (!input || !clearBtn) return false;
            
            input.value = 'نص للمسح';
            clearBtn.click();
            
            return input.value === '';
        });
    }

    testKeyboardShortcuts() {
        this.log('اختبار اختصارات لوحة المفاتيح...');

        this.test('Ctrl+Enter للإرسال', () => {
            const input = document.getElementById('message-input');
            if (!input) return false;
            
            input.value = 'اختبار الإرسال بـ Ctrl+Enter';
            
            const event = new KeyboardEvent('keydown', {
                key: 'Enter',
                ctrlKey: true,
                bubbles: true
            });
            
            let handled = false;
            const handler = (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    handled = true;
                }
            };
            
            document.addEventListener('keydown', handler);
            document.dispatchEvent(event);
            document.removeEventListener('keydown', handler);
            
            return handled;
        });
    }

    testFormValidation() {
        this.log('اختبار التحقق من صحة النموذج...');

        this.test('منع إرسال نص فارغ', () => {
            const form = document.getElementById('chat-form');
            const input = document.getElementById('message-input');
            if (!form || !input) return false;
            
            input.value = '   '; // Only whitespace
            
            let prevented = false;
            const handler = (e) => {
                if (!input.value.trim()) {
                    e.preventDefault();
                    prevented = true;
                }
            };
            
            form.addEventListener('submit', handler);
            
            const event = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(event);
            
            form.removeEventListener('submit', handler);
            return prevented;
        });
    }

    testMessageDisplay() {
        this.log('اختبار عرض الرسائل...');

        this.test('وجود حاوي الرسائل', () => {
            const container = document.getElementById('messages-container');
            return container !== null;
        });

        this.test('وجود مؤشر التفكير', () => {
            const indicator = document.getElementById('thinking-indicator');
            return indicator !== null;
        });
    }

    generateReport() {
        this.log('إنشاء تقرير اختبار نموذج الدردشة...');

        const successRate = ((this.passedTests / this.totalTests) * 100).toFixed(1);
        
        console.log('\n' + '='.repeat(50));
        console.log('💬 تقرير اختبار نموذج الدردشة');
        console.log('='.repeat(50));
        console.log(`📈 إجمالي الاختبارات: ${this.totalTests}`);
        console.log(`✅ نجح: ${this.passedTests}`);
        console.log(`❌ فشل: ${this.failedTests}`);
        console.log(`📊 معدل النجاح: ${successRate}%`);
        console.log('='.repeat(50));

        if (this.failedTests === 0) {
            console.log('🎉 جميع اختبارات نموذج الدردشة نجحت!');
        } else if (successRate >= 80) {
            console.log('👍 معظم اختبارات نموذج الدردشة نجحت.');
        } else {
            console.log('⚠️ هناك مشاكل في نموذج الدردشة تحتاج إلى إصلاح.');
        }

        // Store results globally
        window.chatFormTestResults = {
            total: this.totalTests,
            passed: this.passedTests,
            failed: this.failedTests,
            successRate: successRate,
            details: this.testResults
        };

        return {
            success: this.failedTests === 0,
            successRate: successRate,
            results: this.testResults
        };
    }
}

// Export for manual testing
window.ChatFormTester = ChatFormTester;

// Auto-run if requested
if (window.location.search.includes('test-chat')) {
    setTimeout(() => new ChatFormTester(), 1000);
}
