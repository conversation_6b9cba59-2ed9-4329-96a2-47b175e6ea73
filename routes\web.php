<?php

use App\Http\Controllers\WebController;
use Illuminate\Support\Facades\Route;

// Main route - Modern WIDDX Interface
Route::get('/', function () {
    return view('widdx-modern');
});

// Button test page
Route::get('/test', function () {
    return view('test-buttons');
});

// Settings page
Route::get('/settings', function () {
    return view('settings');
});

// Help page
Route::get('/help', function () {
    return view('help');
});

// About page
Route::get('/about', function () {
    return view('about');
});

// Alternative interfaces for testing
Route::get('/old', function () {
    return view('grok-interface');
});

Route::get('/legacy', [WebController::class, 'index']);

// Health check
Route::get('/up', function () {
    return response()->json(['status' => 'ok', 'timestamp' => now()]);
});

// API routes are defined in routes/api.php
