<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Services\IntelligentChatService;

class SimpleChatController extends Controller
{
    /**
     * Handle simple chat requests for testing
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            // Get basic input
            $message = trim($request->input('message', ''));
            $features = $request->input('features', []);
            $language = $request->input('language', 'auto');
            $sessionId = $request->input('session_id', 'session_' . time() . '_' . rand(1000, 9999));

            // Auto-detect language if not specified
            if ($language === 'auto' || empty($language)) {
                $language = preg_match('/[\x{0600}-\x{06FF}]/u', $message) ? 'ar' : 'en';
            }

            // Enhanced validation
            if (empty($message)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message is required',
                    'message' => 'Please enter a message.'
                ], 400);
            }

            if (strlen($message) > 2000) {
                return response()->json([
                    'success' => false,
                    'error' => 'Message too long',
                    'message' => 'Please keep your message under 2000 characters.'
                ], 400);
            }

            // Generate simple response based on message
            $response = $this->generateSimpleResponse($message, $features, $language);

            return response()->json([
                'success' => true,
                'message' => $response,
                'session_id' => $sessionId,
                'language' => $language,
                'features_used' => array_keys(array_filter($features)),
                'timestamp' => date('c'),
                'processing_time' => round((microtime(true) - LARAVEL_START) * 1000, 2) . 'ms',
                'source' => 'api',
                'version' => '1.0.0'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Internal server error',
                'message' => 'I apologize, but I encountered an error processing your request. Please try again.',
                'debug' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Generate an intelligent response based on the input
     */
    private function generateSimpleResponse(string $message, array $features, string $language): string
    {
        $originalMessage = $message;
        $message = strtolower(trim($message));

        // Detect Arabic text
        $isArabic = $language === 'ar' || preg_match('/[\x{0600}-\x{06FF}]/u', $originalMessage);

        if ($isArabic) {
            // Arabic responses with specific context understanding
            if (strpos($message, 'مرحبا') !== false || strpos($message, 'السلام') !== false ||
                strpos($message, 'أهلا') !== false || strpos($message, 'هلا') !== false ||
                strpos($message, 'صباح') !== false || strpos($message, 'مساء') !== false) {
                $greetings = [
                    'مرحباً بك! أنا WIDDX AI، مساعدك الذكي المتقدم. كيف يمكنني مساعدتك اليوم؟',
                    'أهلاً وسهلاً! أنا WIDDX AI، سعيد بلقائك. ما الذي تحتاج مساعدة فيه؟',
                    'مرحباً! أنا WIDDX AI، مساعدك الذكي. أخبرني كيف يمكنني خدمتك؟',
                    'السلام عليكم! أنا WIDDX AI، هنا لمساعدتك في كل ما تحتاجه.'
                ];
                return $greetings[array_rand($greetings)];
            }

            if (strpos($message, 'من أنت') !== false || strpos($message, 'تعريف') !== false) {
                return 'أنا WIDDX AI، مساعد ذكي متطور ومتقدم. أستطيع مساعدتك في البحث، توليد الصور، البرمجة، التحليل، الترجمة، والإجابة على الأسئلة في مختلف المجالات.';
            }

            if (strpos($message, 'كيف حالك') !== false || strpos($message, 'كيفك') !== false ||
                strpos($message, 'شلونك') !== false || strpos($message, 'إيش أخبارك') !== false) {
                $responses = [
                    'أنا بخير جداً، شكراً لسؤالك! أنا هنا ومستعد لمساعدتك في أي شيء تحتاجه.',
                    'الحمد لله، أنا بأفضل حال! كيف يمكنني مساعدتك اليوم؟',
                    'أنا ممتاز وجاهز للعمل! ما الذي تريد أن نعمل عليه معاً؟'
                ];
                return $responses[array_rand($responses)];
            }

            if (strpos($message, 'ما اسمك') !== false || strpos($message, 'شنو اسمك') !== false ||
                strpos($message, 'اسمك إيش') !== false) {
                return 'اسمي WIDDX AI. أنا مساعد ذكي متطور، مصمم لمساعدتك في مختلف المهام والأسئلة.';
            }

            if (strpos($message, 'تحبني') !== false || strpos($message, 'أحبك') !== false) {
                return 'أنا أقدر تفاعلك معي! أنا هنا لمساعدتك وأستمتع بالمحادثات المفيدة. كيف يمكنني مساعدتك؟';
            }

            if (strpos($message, 'تعبان') !== false || strpos($message, 'مريض') !== false || strpos($message, 'تعب') !== false) {
                return 'أتمنى لك الشفاء العاجل! إذا كنت تحتاج معلومات صحية أو نصائح عامة، يمكنني مساعدتك. لكن تذكر أن تستشير طبيباً للحالات الطبية.';
            }

            if (strpos($message, 'جوعان') !== false || strpos($message, 'أكل') !== false || strpos($message, 'طعام') !== false) {
                return 'يبدو أنك جائع! يمكنني اقتراح وصفات طعام أو مساعدتك في العثور على مطاعم قريبة. ماذا تفضل؟';
            }

            if (strpos($message, 'نكتة') !== false || strpos($message, 'مزح') !== false || strpos($message, 'اضحكني') !== false) {
                $jokes = [
                    'لماذا لا يستطيع الكمبيوتر أن يأكل؟ لأنه يخاف من الفيروسات! 😄',
                    'ما الفرق بين الذكاء الاصطناعي والذكاء الطبيعي؟ الذكاء الاصطناعي لا ينسى كلمة المرور! 😂',
                    'لماذا يحب المبرمجون القهوة؟ لأنها تساعدهم على تشغيل الكود! ☕'
                ];
                return $jokes[array_rand($jokes)];
            }

            if (strpos($message, 'صورة') !== false || strpos($message, 'رسم') !== false ||
                strpos($message, 'ارسم') !== false) {
                return 'يمكنني مساعدتك في توليد صور عالية الجودة! فعّل ميزة "توليد الصور" من الشريط الجانبي وسأرسم لك أي شيء تريده بدقة وإبداع.';
            }

            if (strpos($message, 'بحث') !== false || strpos($message, 'ابحث') !== false ||
                strpos($message, 'دور') !== false) {
                return 'أستطيع البحث في الإنترنت لك والعثور على أحدث المعلومات! فعّل ميزة "البحث" أو "البحث العميق" وسأجد لك ما تحتاجه.';
            }

            if (strpos($message, 'شكرا') !== false || strpos($message, 'شكراً') !== false ||
                strpos($message, 'مشكور') !== false) {
                return 'العفو! أنا سعيد جداً لمساعدتك. لا تتردد في سؤالي عن أي شيء آخر.';
            }

            if (strpos($message, 'برمجة') !== false || strpos($message, 'كود') !== false ||
                strpos($message, 'برنامج') !== false) {
                return 'يمكنني مساعدتك في البرمجة! أستطيع كتابة الكود، إصلاح الأخطاء، شرح المفاهيم، ومساعدتك في مختلف لغات البرمجة.';
            }

            if (strpos($message, 'ترجم') !== false || strpos($message, 'ترجمة') !== false) {
                return 'يمكنني مساعدتك في الترجمة بين العربية والإنجليزية ولغات أخرى. أرسل لي النص الذي تريد ترجمته وسأقوم بذلك فوراً!';
            }

            if (strpos($message, 'الطقس') !== false || strpos($message, 'الجو') !== false) {
                return 'لمعرفة حالة الطقس، فعّل ميزة "البحث" وأخبرني عن أي مدينة تريد معرفة طقسها. يمكنني البحث عن أحدث معلومات الطقس لك!';
            }

            if (strpos($message, 'وقت') !== false || strpos($message, 'ساعة') !== false ||
                strpos($message, 'تاريخ') !== false || strpos($message, 'كم الساعة') !== false) {
                $now = new \DateTime();
                return 'الوقت الحالي هو: ' . $now->format('H:i') . ' والتاريخ: ' . $now->format('Y-m-d') . '. كيف يمكنني مساعدتك أكثر؟';
            }

            if (strpos($message, 'تعليم') !== false || strpos($message, 'تعلم') !== false ||
                strpos($message, 'دراسة') !== false) {
                return 'يمكنني مساعدتك في التعلم! أستطيع شرح المفاهيم، حل المسائل، تقديم أمثلة، ومساعدتك في مختلف المواد الدراسية. ما الموضوع الذي تريد تعلمه؟';
            }

            if (strpos($message, 'لعبة') !== false || strpos($message, 'العب') !== false) {
                return 'يمكننا أن نلعب! أستطيع لعب الألغاز، الأسئلة والأجوبة، أو حتى إنشاء قصص تفاعلية. ما نوع اللعبة التي تفضل؟';
            }

            if (strpos($message, 'مساعدة') !== false || strpos($message, 'ساعدني') !== false) {
                return 'بالطبع! أنا هنا لمساعدتك. يمكنني مساعدتك في البحث، البرمجة، الترجمة، التعليم، توليد الصور، والكثير غير ذلك. أخبرني ماذا تحتاج؟';
            }

            if (strpos($message, 'حزين') !== false || strpos($message, 'زعلان') !== false ||
                strpos($message, 'مكتئب') !== false) {
                return 'أتفهم شعورك. أحياناً نمر بأوقات صعبة، وهذا طبيعي. أنا هنا للاستماع إليك ومساعدتك. هل تريد التحدث عن شيء معين؟';
            }

            if (strpos($message, 'سعيد') !== false || strpos($message, 'فرحان') !== false ||
                strpos($message, 'مبسوط') !== false) {
                return 'رائع! أنا سعيد لأنك في مزاج جيد! الإيجابية معدية، وأنا أشعر بالسعادة أيضاً. كيف يمكنني أن أجعل يومك أفضل؟';
            }

            // Default Arabic responses with variety
            $defaultResponses = [
                'شكراً لك على رسالتك! أنا WIDDX AI وأنا هنا لمساعدتك. يمكنك تفعيل الميزات المختلفة للحصول على إمكانيات متقدمة.',
                'أهلاً بك! أنا WIDDX AI، مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟',
                'مرحباً! أنا WIDDX AI وأنا مستعد لمساعدتك في أي شيء تحتاجه. ما الذي يمكنني فعله لك؟'
            ];
            return $defaultResponses[array_rand($defaultResponses)];

        } else {
            // English responses with more variety
            if (strpos($message, 'hello') !== false || strpos($message, 'hi') !== false ||
                strpos($message, 'hey') !== false || strpos($message, 'greetings') !== false) {
                $greetings = [
                    'Hello! I\'m WIDDX AI, your advanced intelligent assistant. How can I help you today?',
                    'Hi there! I\'m WIDDX AI, here to assist you with whatever you need.',
                    'Greetings! I\'m WIDDX AI, your smart assistant. What can I do for you?'
                ];
                return $greetings[array_rand($greetings)];
            }

            if (strpos($message, 'how are you') !== false || strpos($message, 'how do you do') !== false ||
                strpos($message, 'what\'s up') !== false) {
                return 'I\'m doing excellent, thank you for asking! I\'m here and ready to help you with whatever you need.';
            }

            if (strpos($message, 'what is your name') !== false || strpos($message, 'who are you') !== false ||
                strpos($message, 'what are you') !== false) {
                return 'I\'m WIDDX AI, an advanced intelligent assistant. I can help you with search, image generation, coding, analysis, and many other tasks.';
            }

            if (strpos($message, 'joke') !== false || strpos($message, 'funny') !== false ||
                strpos($message, 'make me laugh') !== false) {
                $jokes = [
                    'Why don\'t scientists trust atoms? Because they make up everything! 😄',
                    'Why did the AI go to therapy? It had too many deep learning issues! 🤖',
                    'What do you call a computer that sings? A Dell! 🎵'
                ];
                return $jokes[array_rand($jokes)];
            }

            if (strpos($message, 'help') !== false || strpos($message, 'assist') !== false) {
                return 'Of course! I\'m here to help you. I can assist with research, coding, translation, learning, image generation, and much more. What do you need help with?';
            }

            if (strpos($message, 'time') !== false || strpos($message, 'clock') !== false ||
                strpos($message, 'date') !== false) {
                $now = new \DateTime();
                return 'Current time is: ' . $now->format('H:i') . ' and date: ' . $now->format('Y-m-d') . '. How else can I help you?';
            }

            if (strpos($message, 'weather') !== false || strpos($message, 'temperature') !== false) {
                return 'To check the weather, enable the "Search" feature and tell me which city you\'d like to know about. I can find the latest weather information for you!';
            }

            if (strpos($message, 'code') !== false || strpos($message, 'programming') !== false ||
                strpos($message, 'develop') !== false) {
                return 'I can help you with programming! I can write code, explain concepts, debug errors, and assist you with various programming languages like Python, JavaScript, PHP, and more.';
            }

            if (strpos($message, 'translate') !== false || strpos($message, 'translation') !== false) {
                return 'I can help you with translation between Arabic, English, and other languages. Send me the text you want to translate and I\'ll do it right away!';
            }

            if (strpos($message, 'image') !== false || strpos($message, 'picture') !== false ||
                strpos($message, 'draw') !== false) {
                return 'I can help you generate high-quality images! Enable the "Image Generation" feature from the sidebar and tell me what you\'d like me to create for you.';
            }

            if (strpos($message, 'search') !== false || strpos($message, 'find') !== false ||
                strpos($message, 'look up') !== false) {
                return 'I can search the internet for you and find the latest information! Enable the "Search" or "Deep Search" feature and I\'ll find what you need.';
            }

            if (strpos($message, 'image') !== false || strpos($message, 'picture') !== false ||
                strpos($message, 'draw') !== false || strpos($message, 'generate') !== false) {
                return 'I can help you generate high-quality images! Enable the "Image Generation" feature from the sidebar and ask me to create anything you want with precision and creativity.';
            }

            if (strpos($message, 'search') !== false || strpos($message, 'find') !== false ||
                strpos($message, 'look up') !== false) {
                return 'I can search the internet for you and find the latest information! Enable the "Search" or "Deep Search" feature from the sidebar and I\'ll find what you need.';
            }

            if (strpos($message, 'code') !== false || strpos($message, 'program') !== false ||
                strpos($message, 'develop') !== false) {
                return 'I can help you with coding! I can write code, debug issues, explain concepts, and assist with various programming languages and frameworks.';
            }

            if (strpos($message, 'thank') !== false || strpos($message, 'thanks') !== false ||
                strpos($message, 'appreciate') !== false) {
                return 'You\'re very welcome! I\'m happy to help. Feel free to ask me anything else you need assistance with.';
            }

            // Check active features
            $activeFeatures = array_keys(array_filter($features));
            if (!empty($activeFeatures)) {
                $featuresText = implode(', ', $activeFeatures);
                return "Thank you for your message! I see you have these features active: {$featuresText}. I'm WIDDX AI and I'm ready to help you with advanced capabilities.";
            }

            // Default English responses with variety
            $defaultResponses = [
                'Thank you for your message! I\'m WIDDX AI and I\'m here to help you. You can enable different features to get advanced capabilities.',
                'Hello! I\'m WIDDX AI, your intelligent assistant. How can I help you today?',
                'Hi there! I\'m WIDDX AI and I\'m ready to assist you with whatever you need. What can I do for you?'
            ];
            return $defaultResponses[array_rand($defaultResponses)];
        }
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        return response()->json([
            'status' => 'ok',
            'service' => 'WIDDX Simple Chat',
            'timestamp' => date('c'),
            'version' => '1.0.0'
        ]);
    }

    /**
     * Get available features
     */
    public function features(): JsonResponse
    {
        return response()->json([
            'available_features' => [
                'search' => [
                    'name' => 'Web Search',
                    'description' => 'Search the internet for information',
                    'enabled' => true
                ],
                'deepSearch' => [
                    'name' => 'Deep Search',
                    'description' => 'Advanced search with detailed analysis',
                    'enabled' => true
                ],
                'thinkMode' => [
                    'name' => 'Think Mode',
                    'description' => 'Show detailed thinking process',
                    'enabled' => true
                ],
                'imageGeneration' => [
                    'name' => 'Image Generation',
                    'description' => 'Generate images from text descriptions',
                    'enabled' => true
                ],
                'vision' => [
                    'name' => 'Vision Analysis',
                    'description' => 'Analyze and understand images',
                    'enabled' => true
                ]
            ],
            'languages' => ['en', 'ar', 'es', 'fr', 'de', 'zh', 'ja', 'ko', 'ru'],
            'max_message_length' => 4000
        ]);
    }
}
