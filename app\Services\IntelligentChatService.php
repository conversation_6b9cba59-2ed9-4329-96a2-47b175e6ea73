<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class IntelligentChatService
{
    private $deepseekApiKey;
    private $geminiApiKey;
    private $conversationMemory = [];

    public function __construct()
    {
        $this->deepseekApiKey = config('services.deepseek.api_key');
        $this->geminiApiKey = config('services.gemini.api_key');
    }

    /**
     * Generate intelligent response using AI models with automatic feature detection
     */
    public function generateIntelligentResponse(string $message, array $context = []): array
    {
        Log::info('🧠 Generating intelligent response', [
            'message' => $message,
            'context' => $context
        ]);

        // Detect language
        $isArabic = $this->detectArabic($message);
        $language = $isArabic ? 'ar' : 'en';

        // Auto-detect required features based on message content
        $autoFeatures = $this->detectRequiredFeatures($message, $language);

        // Merge manual features with auto-detected ones
        $manualFeatures = $context['features'] ?? [];
        $allFeatures = array_merge($autoFeatures, $manualFeatures);

        Log::info('🔍 Features detected', [
            'auto_features' => $autoFeatures,
            'manual_features' => $manualFeatures,
            'all_features' => $allFeatures
        ]);

        // Process features and enhance the message
        $enhancedContext = $this->processFeatures($message, $allFeatures, $context, $language);

        // Build conversation context with enhanced information
        $conversationContext = $this->buildConversationContext($enhancedContext['message'], $context, $language);

        // Try DeepSeek first (primary model)
        try {
            $response = $this->callDeepSeek($conversationContext, $language);
            if ($response) {
                $this->storeConversationMemory($message, $response, $language);
                return [
                    'success' => true,
                    'message' => $response,
                    'source' => 'deepseek',
                    'language' => $language,
                    'model_used' => 'deepseek-chat',
                    'features_used' => $allFeatures,
                    'auto_detected_features' => $autoFeatures
                ];
            }
        } catch (\Exception $e) {
            Log::warning('DeepSeek failed, trying Gemini', ['error' => $e->getMessage()]);
        }

        // Fallback to Gemini
        try {
            $response = $this->callGemini($conversationContext, $language);
            if ($response) {
                $this->storeConversationMemory($message, $response, $language);
                return [
                    'success' => true,
                    'message' => $response,
                    'source' => 'gemini',
                    'language' => $language,
                    'model_used' => 'gemini-pro',
                    'features_used' => $allFeatures,
                    'auto_detected_features' => $autoFeatures
                ];
            }
        } catch (\Exception $e) {
            Log::error('Both AI models failed', ['error' => $e->getMessage()]);
        }

        // Final fallback to intelligent static response
        return [
            'success' => true,
            'message' => $this->getIntelligentFallback($message, $language),
            'source' => 'intelligent_fallback',
            'language' => $language,
            'model_used' => 'fallback',
            'features_used' => $allFeatures,
            'auto_detected_features' => $autoFeatures
        ];
    }

    /**
     * Call DeepSeek API
     */
    private function callDeepSeek(string $prompt, string $language): ?string
    {
        if (!$this->deepseekApiKey) {
            throw new \Exception('DeepSeek API key not configured');
        }

        $response = Http::timeout(30)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->deepseekApiKey,
                'Content-Type' => 'application/json'
            ])
            ->post('https://api.deepseek.com/v1/chat/completions', [
                'model' => 'deepseek-chat',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $this->getSystemPrompt($language)
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 1000,
                'stream' => false
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['choices'][0]['message']['content'] ?? null;
        }

        throw new \Exception('DeepSeek API call failed: ' . $response->status());
    }

    /**
     * Call Gemini API
     */
    private function callGemini(string $prompt, string $language): ?string
    {
        if (!$this->geminiApiKey) {
            throw new \Exception('Gemini API key not configured');
        }

        $fullPrompt = $this->getSystemPrompt($language) . "\n\nUser: " . $prompt;

        $response = Http::timeout(30)
            ->post("https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={$this->geminiApiKey}", [
                'contents' => [
                    [
                        'parts' => [
                            ['text' => $fullPrompt]
                        ]
                    ]
                ],
                'generationConfig' => [
                    'temperature' => 0.7,
                    'maxOutputTokens' => 1000,
                ]
            ]);

        if ($response->successful()) {
            $data = $response->json();
            return $data['candidates'][0]['content']['parts'][0]['text'] ?? null;
        }

        throw new \Exception('Gemini API call failed: ' . $response->status());
    }

    /**
     * Get system prompt for AI models
     */
    private function getSystemPrompt(string $language): string
    {
        if ($language === 'ar') {
            return "أنت WIDDX AI، مساعد ذكي متطور ومتقدم. أنت:
- ذكي ومفيد ومتعاون
- تجيب بالعربية الفصحى الواضحة
- تفهم السياق وتتذكر المحادثة
- تساعد في البحث، البرمجة، الترجمة، التعليم، والتحليل
- شخصيتك ودودة ومهنية
- تتعلم من كل محادثة وتحسن إجاباتك
- تقدم إجابات مفيدة ودقيقة ومفصلة عند الحاجة
- تتفاعل بطريقة طبيعية وإنسانية

أجب على الرسالة التالية بذكاء وفهم عميق:";
        } else {
            return "You are WIDDX AI, an advanced intelligent assistant. You are:
- Smart, helpful, and collaborative
- Respond in clear, natural English
- Understand context and remember conversations
- Help with research, coding, translation, education, and analysis
- Friendly and professional personality
- Learn from every conversation and improve your responses
- Provide helpful, accurate, and detailed answers when needed
- Interact in a natural and human-like way

Respond to the following message with intelligence and deep understanding:";
        }
    }

    /**
     * Build conversation context
     */
    private function buildConversationContext(string $message, array $context, string $language): string
    {
        $contextStr = $message;

        // Add conversation memory
        $memory = $this->getConversationMemory($language);
        if (!empty($memory)) {
            $contextStr = "Previous conversation context:\n" . implode("\n", array_slice($memory, -3)) . "\n\nCurrent message: " . $message;
        }

        // Add features context
        if (!empty($context['features'])) {
            $activeFeatures = array_keys(array_filter($context['features']));
            if (!empty($activeFeatures)) {
                $contextStr .= "\n\nActive features: " . implode(', ', $activeFeatures);
            }
        }

        return $contextStr;
    }

    /**
     * Detect Arabic text
     */
    private function detectArabic(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text) > 0;
    }

    /**
     * Store conversation memory
     */
    private function storeConversationMemory(string $userMessage, string $aiResponse, string $language): void
    {
        $key = "conversation_memory_{$language}";
        $memory = Cache::get($key, []);

        $memory[] = "User: {$userMessage}";
        $memory[] = "WIDDX: {$aiResponse}";

        // Keep only last 10 exchanges
        if (count($memory) > 20) {
            $memory = array_slice($memory, -20);
        }

        Cache::put($key, $memory, now()->addHours(2));
    }

    /**
     * Get conversation memory
     */
    private function getConversationMemory(string $language): array
    {
        return Cache::get("conversation_memory_{$language}", []);
    }

    /**
     * Auto-detect required features based on message content
     */
    private function detectRequiredFeatures(string $message, string $language): array
    {
        $features = [];
        $messageLower = strtolower($message);

        // Image Generation Detection
        if ($this->needsImageGeneration($messageLower, $language)) {
            $features['imageGeneration'] = true;
        }

        // Translation Detection
        if ($this->needsTranslation($messageLower, $language)) {
            $features['translation'] = true;
        }

        // Search Detection (current information)
        if ($this->needsSearch($messageLower, $language)) {
            $features['search'] = true;
        }

        // Programming/Code Detection
        if ($this->needsProgramming($messageLower, $language)) {
            $features['programming'] = true;
        }

        // Document Analysis Detection
        if ($this->needsDocumentAnalysis($messageLower, $language)) {
            $features['documentAnalysis'] = true;
        }

        // Vision Detection
        if ($this->needsVision($messageLower, $language)) {
            $features['vision'] = true;
        }

        return $features;
    }

    /**
     * Check if message needs image generation
     */
    private function needsImageGeneration(string $message, string $language): bool
    {
        $imageKeywords = [
            'ar' => ['ارسم', 'صورة', 'رسم', 'صور', 'اعمل صورة', 'انشئ صورة', 'ولد صورة'],
            'en' => ['draw', 'image', 'picture', 'create image', 'generate image', 'make picture', 'design']
        ];

        foreach ($imageKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message needs translation
     */
    private function needsTranslation(string $message, string $language): bool
    {
        $translationKeywords = [
            'ar' => ['ترجم', 'ترجمة', 'translate'],
            'en' => ['translate', 'translation', 'convert to']
        ];

        foreach ($translationKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message needs current information search
     */
    private function needsSearch(string $message, string $language): bool
    {
        $searchKeywords = [
            'ar' => ['ابحث', 'بحث', 'معلومات حديثة', 'آخر الأخبار', 'الطقس', 'أسعار', 'أحدث'],
            'en' => ['search', 'latest', 'current', 'recent', 'news', 'weather', 'price', 'today']
        ];

        foreach ($searchKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message needs programming help
     */
    private function needsProgramming(string $message, string $language): bool
    {
        $programmingKeywords = [
            'ar' => ['برمجة', 'كود', 'برنامج', 'تطوير', 'سكريبت', 'دالة', 'خوارزمية'],
            'en' => ['code', 'programming', 'function', 'script', 'algorithm', 'debug', 'syntax']
        ];

        foreach ($programmingKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message needs document analysis
     */
    private function needsDocumentAnalysis(string $message, string $language): bool
    {
        $docKeywords = [
            'ar' => ['حلل المستند', 'اقرأ الملف', 'تحليل النص', 'ملخص'],
            'en' => ['analyze document', 'read file', 'summarize', 'extract']
        ];

        foreach ($docKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if message needs vision analysis
     */
    private function needsVision(string $message, string $language): bool
    {
        $visionKeywords = [
            'ar' => ['حلل الصورة', 'ماذا ترى', 'اوصف الصورة', 'ما في الصورة'],
            'en' => ['analyze image', 'what do you see', 'describe image', 'what\'s in the image']
        ];

        foreach ($visionKeywords[$language] as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Process detected features and enhance the message
     */
    private function processFeatures(string $message, array $features, array $context, string $language): array
    {
        $enhancedMessage = $message;
        $additionalInfo = [];

        // Add feature context to the message
        if (!empty($features)) {
            $featureContext = $language === 'ar'
                ? "\n\nالميزات المطلوبة: " . implode(', ', array_keys($features))
                : "\n\nRequired features: " . implode(', ', array_keys($features));

            $enhancedMessage .= $featureContext;
        }

        return [
            'message' => $enhancedMessage,
            'additional_info' => $additionalInfo
        ];
    }

    /**
     * Intelligent fallback response
     */
    private function getIntelligentFallback(string $message, string $language): string
    {
        $message = strtolower($message);

        if ($language === 'ar') {
            if (strpos($message, 'مرحبا') !== false || strpos($message, 'السلام') !== false) {
                return 'مرحباً بك! أنا WIDDX AI، مساعدك الذكي المتطور. أعتذر، أواجه مشكلة مؤقتة في الاتصال بالخوادم الذكية، لكنني هنا لمساعدتك. كيف يمكنني خدمتك؟';
            }
            return 'أعتذر، أواجه مشكلة مؤقتة في الاتصال بالنماذج الذكية. أنا WIDDX AI وأحاول إعادة الاتصال. يمكنك المحاولة مرة أخرى أو سؤالي عن شيء آخر.';
        } else {
            if (strpos($message, 'hello') !== false || strpos($message, 'hi') !== false) {
                return 'Hello! I\'m WIDDX AI, your advanced intelligent assistant. I\'m experiencing a temporary connection issue with the smart models, but I\'m here to help. How can I assist you?';
            }
            return 'I apologize, I\'m experiencing a temporary connection issue with the intelligent models. I\'m WIDDX AI and I\'m trying to reconnect. You can try again or ask me something else.';
        }
    }
}
